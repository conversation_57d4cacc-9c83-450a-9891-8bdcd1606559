{"scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server.js", "vercel-build": "vite build"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@types/google-one-tap": "^1.2.6", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.18.2", "express-validator": "^7.2.1", "framer-motion": "^12.19.1", "jsonwebtoken": "^9.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.3", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "terser": "^5.43.1", "vite": "^4.3.9"}}